#!/usr/bin/env python3

"""
Tactical RMM Network Scanner
Scanner de rede para uso no Tactical RMM
Identifica dispositivos, sistemas operacionais e modelos específicos
Autor: <PERSON>
"""

import subprocess
import threading
import ipaddress
import socket
import sys
import time
import json
import argparse
from datetime import datetime
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed

# Tentar importar openpyxl para Excel
try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

class TacticalNetworkScanner:
    def __init__(self, network, max_workers=50, timeout=2):
        self.network = ipaddress.IPv4Network(network)
        self.max_workers = max_workers
        self.timeout = timeout
        self.results = []
        self.lock = threading.Lock()
        
        # Portas comuns para identificação
        self.common_ports = {
            22: "SSH", 23: "Telnet", 25: "SMTP", 53: "DNS", 80: "HTTP",
            110: "POP3", 135: "RPC", 139: "NetBIOS", 143: "IMAP", 443: "HTTPS",
            445: "SMB", 993: "IMAPS", 995: "POP3S", 1900: "UPnP", 3389: "RDP",
            5000: "UPnP", 5353: "mDNS", 5900: "VNC", 8080: "HTTP-Alt",
            8443: "HTTPS-Alt", 9100: "Printer", 10000: "Webmin", 62078: "iPhone-Sync"
        }
        
        # Assinaturas de OS por TTL
        self.os_signatures = {
            64: "Linux/Unix", 128: "Windows", 255: "Cisco/Network Device",
            32: "Windows 95/98", 60: "macOS/iOS"
        }
        
        # Padrões de hostname
        self.hostname_patterns = {
            'iphone': 'iPhone', 'ipad': 'iPad', 'android': 'Android Device',
            'samsung': 'Samsung Device', 'xiaomi': 'Xiaomi Device',
            'tp-link': 'TP-Link Router', 'tplink': 'TP-Link Router',
            'dlink': 'D-Link Router', 'd-link': 'D-Link Router',
            'linksys': 'Linksys Router', 'netgear': 'Netgear Router',
            'asus': 'ASUS Router', 'cisco': 'Cisco Device',
            'mikrotik': 'MikroTik Router', 'ubiquiti': 'Ubiquiti Device',
            'unifi': 'UniFi Access Point', 'printer': 'Network Printer',
            'hp': 'HP Printer', 'canon': 'Canon Printer', 'epson': 'Epson Printer',
            'ubuntu': 'Ubuntu Server', 'debian': 'Debian Server',
            'windows': 'Windows Machine', 'server': 'Server',
            'nas': 'NAS Device', 'synology': 'Synology NAS', 'qnap': 'QNAP NAS'
        }
        
        # Base de modelos conhecidos
        self.known_models = {
            'archer-c5': 'TP-Link Archer C5 Router',
            'archer-c7': 'TP-Link Archer C7 Router',
            'archer-c9': 'TP-Link Archer C9 Router',
            'dir-615': 'D-Link DIR-615 Router',
            'dir-842': 'D-Link DIR-842 Router',
            'r6120': 'Netgear R6120 Router',
            'r7000': 'Netgear R7000 Nighthawk Router',
            'rt-ac68u': 'ASUS RT-AC68U Router',
            'rt-ax55': 'ASUS RT-AX55 WiFi 6 Router',
            'uap-ac-lite': 'Ubiquiti UniFi AP AC Lite',
            'udm-pro': 'Ubiquiti Dream Machine Pro'
        }

    def ping_host(self, ip):
        """Verifica se o host está ativo via ping"""
        try:
            if sys.platform.startswith('win'):
                cmd = ['ping', '-n', '1', '-w', str(self.timeout * 1000), str(ip)]
            else:
                cmd = ['ping', '-c', '1', '-W', str(self.timeout), str(ip)]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout + 1)
            
            if result.returncode == 0:
                ttl = self.extract_ttl(result.stdout)
                return True, ttl
            return False, None
        except:
            return False, None

    def extract_ttl(self, ping_output):
        """Extrai TTL do output do ping"""
        try:
            if sys.platform.startswith('win'):
                for line in ping_output.split('\n'):
                    if 'TTL=' in line:
                        ttl = line.split('TTL=')[1].split()[0]
                        return int(ttl)
            else:
                for line in ping_output.split('\n'):
                    if 'ttl=' in line:
                        ttl = line.split('ttl=')[1].split()[0]
                        return int(ttl)
        except:
            pass
        return None

    def get_hostname(self, ip):
        """Tenta resolver o hostname do IP"""
        try:
            hostname = socket.gethostbyaddr(str(ip))[0]
            return hostname
        except:
            return None

    def scan_ports(self, ip, ports=None):
        """Escaneia portas específicas do host"""
        if ports is None:
            ports = [22, 23, 80, 135, 139, 443, 445, 3389, 5353, 8080, 9100, 62078]
        
        open_ports = []
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.5)
                result = sock.connect_ex((str(ip), port))
                if result == 0:
                    service = self.common_ports.get(port, f"Port-{port}")
                    open_ports.append((port, service))
                sock.close()
            except:
                pass
        return open_ports

    def identify_model_from_hostname(self, hostname):
        """Identifica modelo específico baseado no hostname"""
        if not hostname or hostname == "N/A":
            return None
            
        hostname_lower = hostname.lower()
        
        # Buscar por modelos conhecidos
        for model_key, model_name in self.known_models.items():
            if model_key in hostname_lower:
                return model_name
        
        # Buscar por padrões usando regex
        import re
        patterns = [
            (r'archer[_-]?([a-z0-9]+)', 'TP-Link Archer {}'),
            (r'dir[_-]?([0-9]+)', 'D-Link DIR-{}'),
            (r'r([0-9]+)', 'Netgear R{}'),
            (r'rt[_-]?ac([0-9]+[a-z]*)', 'ASUS RT-AC{}'),
        ]
        
        for pattern, format_str in patterns:
            match = re.search(pattern, hostname_lower)
            if match:
                model = match.group(1).upper()
                return format_str.format(model)
        
        return None

    def identify_os(self, ttl, open_ports, hostname):
        """Identifica o sistema operacional"""
        os_info = "Desconhecido"
        confidence = 0
        detection_methods = []
        
        # Identificação por TTL
        if ttl:
            for ttl_sig, os_name in self.os_signatures.items():
                if abs(ttl - ttl_sig) <= 5:
                    os_info = os_name
                    confidence = 70
                    detection_methods.append(f"TTL={ttl}")
                    break
        
        # Refinamento por portas
        port_numbers = [port for port, _ in open_ports]
        
        if 445 in port_numbers or 135 in port_numbers or 3389 in port_numbers:
            if "Windows" not in os_info:
                os_info = "Windows"
                confidence = 80
            detection_methods.append("Ports")
                
        if 22 in port_numbers and ttl and 60 <= ttl <= 68:
            os_info = "Linux"
            confidence = 85
            if "Ports" not in [m.split("=")[0] for m in detection_methods]:
                detection_methods.append("Ports")
        
        # Refinamento por hostname
        if hostname and hostname != "N/A":
            hostname_lower = hostname.lower()
            if any(x in hostname_lower for x in ['windows', 'win', 'pc']):
                os_info = "Windows"
                confidence = max(confidence, 75)
                detection_methods.append("Hostname")
            elif any(x in hostname_lower for x in ['linux', 'ubuntu', 'debian']):
                os_info = "Linux"
                confidence = max(confidence, 75)
                detection_methods.append("Hostname")
        
        if not detection_methods:
            detection_methods.append("Unknown")
        
        return os_info, confidence, "+".join(detection_methods)

    def get_device_type(self, open_ports, hostname, os_info):
        """Determina o tipo específico de dispositivo"""
        port_numbers = [port for port, _ in open_ports]
        hostname_lower = hostname.lower() if hostname and hostname != "N/A" else ""
        
        # Identificação por modelo específico
        specific_model = self.identify_model_from_hostname(hostname)
        if specific_model:
            return specific_model
        
        # Identificação por padrões gerais
        for pattern, device_name in self.hostname_patterns.items():
            if pattern in hostname_lower:
                return device_name
        
        # Identificação por portas
        if 62078 in port_numbers:
            return "iPhone/iPad"
        elif 3389 in port_numbers:
            return "Windows Server/Desktop"
        elif 22 in port_numbers and len(port_numbers) >= 3:
            return "Linux Server"
        elif 9100 in port_numbers:
            return "Network Printer"
        elif any(p in port_numbers for p in [23, 161, 162]):
            return "Network Device"
        elif 80 in port_numbers or 443 in port_numbers:
            return "Web Server/Router"
        
        return "Unknown Device"

    def scan_host(self, ip):
        """Escaneia um host específico"""
        try:
            # Ping test
            is_alive, ttl = self.ping_host(ip)
            if not is_alive:
                return None
            
            # Resolver hostname
            hostname = self.get_hostname(ip)
            
            # Scan de portas
            open_ports = self.scan_ports(ip)
            
            # Identificar OS
            os_info, confidence, detection_method = self.identify_os(ttl, open_ports, hostname)
            
            # Determinar tipo de dispositivo
            device_type = self.get_device_type(open_ports, hostname, os_info)
            
            result = {
                'ip': str(ip),
                'hostname': hostname or 'N/A',
                'ttl': ttl,
                'os': os_info,
                'confidence': confidence,
                'detection_method': detection_method,
                'device_type': device_type,
                'open_ports': open_ports,
                'scan_time': datetime.now().isoformat()
            }
            
            with self.lock:
                self.results.append(result)
                # Output para Tactical RMM
                print(f"FOUND: {str(ip)} | {hostname or 'N/A'} | {os_info} | {device_type}")
            
            return result
        except Exception as e:
            return None

    def scan_network(self):
        """Escaneia toda a rede"""
        print(f"TACTICAL-SCAN-START: {self.network} | {self.network.num_addresses} IPs | {self.max_workers} threads")
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {executor.submit(self.scan_host, ip): ip for ip in self.network.hosts()}
            
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception:
                    pass
        
        end_time = time.time()
        
        # Resumo para Tactical RMM
        print(f"TACTICAL-SCAN-COMPLETE: {len(self.results)} devices found in {end_time - start_time:.1f}s")
        
        # Estatísticas
        os_stats = {}
        device_stats = {}
        
        for result in self.results:
            os_name = result['os']
            device_type = result['device_type']
            os_stats[os_name] = os_stats.get(os_name, 0) + 1
            device_stats[device_type] = device_stats.get(device_type, 0) + 1
        
        print("TACTICAL-STATS-OS:", json.dumps(os_stats))
        print("TACTICAL-STATS-DEVICES:", json.dumps(device_stats))
        
        return self.results

    def save_to_excel(self, filename=None):
        """Salva resultados em arquivo Excel para Tactical RMM"""
        if not EXCEL_AVAILABLE:
            print("TACTICAL-ERROR: openpyxl não instalado. Execute: pip install openpyxl")
            return False

        if filename is None:
            filename = f"tactical_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        try:
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Network Scan Results"

            # Cabeçalhos
            headers = ["IP", "Hostname", "OS", "Dispositivo/Modelo", "Método", "TTL", "Portas", "Data/Hora"]

            # Estilizar cabeçalho
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill

            # Adicionar dados
            for row, result in enumerate(self.results, 2):
                ws.cell(row=row, column=1, value=result['ip'])
                ws.cell(row=row, column=2, value=result['hostname'])
                ws.cell(row=row, column=3, value=result['os'])
                ws.cell(row=row, column=4, value=result['device_type'])
                ws.cell(row=row, column=5, value=result['detection_method'])
                ws.cell(row=row, column=6, value=result['ttl'])

                ports_str = ", ".join([f"{port}" for port, _ in result['open_ports']])
                ws.cell(row=row, column=7, value=ports_str)

                scan_time = datetime.fromisoformat(result['scan_time']).strftime('%d/%m/%Y %H:%M:%S')
                ws.cell(row=row, column=8, value=scan_time)

            # Ajustar larguras
            column_widths = [15, 25, 15, 30, 15, 8, 20, 20]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[get_column_letter(col)].width = width

            wb.save(filename)
            print(f"TACTICAL-EXCEL: {filename}")
            return True

        except Exception as e:
            print(f"TACTICAL-ERROR: Erro ao salvar Excel: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='Tactical RMM Network Scanner')
    parser.add_argument('network', help='Rede para escanear (ex: ***********/24)')
    parser.add_argument('--threads', '-t', type=int, default=50, help='Número de threads')
    parser.add_argument('--timeout', type=int, default=2, help='Timeout em segundos')
    parser.add_argument('--json', action='store_true', help='Saída em JSON')
    parser.add_argument('--excel', action='store_true', help='Exportar para Excel')
    parser.add_argument('--output', type=str, help='Nome do arquivo de saída')
    
    args = parser.parse_args()
    
    try:
        scanner = TacticalNetworkScanner(
            network=args.network,
            max_workers=args.threads,
            timeout=args.timeout
        )
        
        results = scanner.scan_network()
        
        if args.json:
            output = {
                'scan_info': {
                    'network': args.network,
                    'scan_time': datetime.now().isoformat(),
                    'total_hosts': len(results)
                },
                'results': results
            }
            print("TACTICAL-JSON:", json.dumps(output))

        if args.excel:
            if args.output:
                filename = f"{args.output}.xlsx"
                scanner.save_to_excel(filename)
            else:
                scanner.save_to_excel()
            
    except KeyboardInterrupt:
        print("TACTICAL-ERROR: Scan interrupted by user")
    except Exception as e:
        print(f"TACTICAL-ERROR: {e}")

if __name__ == "__main__":
    main()
