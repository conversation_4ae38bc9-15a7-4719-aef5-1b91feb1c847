#!/bin/bash

# Bootstrap Script - Raspberry Pi First Boot
# Este script baixa e executa a versão mais atual do GitHub
# Autor: <PERSON> - NVirtual
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configurações do repositório GitHub
GITHUB_TOKEN="****************************************"
GITHUB_REPO="nubium-cloud/Raspberry-First-Boot-Configuration"
SCRIPT_NAME="setup_first_boot.sh"
GITHUB_URL="https://raw.githubusercontent.com/${GITHUB_REPO}/main/${SCRIPT_NAME}"

# Arquivo de flag para verificar se já foi executado
FIRST_BOOT_FLAG="/var/lib/first_boot_completed"

# Função para limpar tela e mostrar header
show_header() {
    clear > /dev/tty1 2>/dev/null || true
    local header="
╔══════════════════════════════════════════════════════════════╗
║                RASPBERRY PI BOOTSTRAP                       ║
║            Baixando Configuração Automática                 ║
║                                                              ║
║  📥 Baixando do GitHub...                                   ║
║  🔧 Executando instalação automática                        ║
║                                                              ║
║  Desenvolvido por: Paulo Matheus - NVirtual                ║
╚══════════════════════════════════════════════════════════════╝
"
    echo -e "${GREEN}$header${NC}" > /dev/tty1 2>/dev/null || true
    echo -e "${GREEN}$header${NC}"
}

# Função para log com exibição na tela
log() {
    local message="✅ [$(date '+%H:%M:%S')] $1"
    echo -e "${GREEN}$message${NC}"
    echo -e "${GREEN}$message${NC}" > /dev/tty1 2>/dev/null || true
}

error() {
    local message="❌ [ERRO] $1"
    echo -e "${RED}$message${NC}"
    echo -e "${RED}$message${NC}" > /dev/tty1 2>/dev/null || true
    exit 1
}

warning() {
    local message="⚠️  [AVISO] $1"
    echo -e "${YELLOW}$message${NC}"
    echo -e "${YELLOW}$message${NC}" > /dev/tty1 2>/dev/null || true
}

info() {
    local message="ℹ️  [INFO] $1"
    echo -e "${BLUE}$message${NC}"
    echo -e "${BLUE}$message${NC}" > /dev/tty1 2>/dev/null || true
}

# Função para mostrar status atual
show_status() {
    local status="$1"
    local details="$2"
    local status_msg="
┌─────────────────────────────────────────────────────────────┐
│ STATUS: $status
│ $details
└─────────────────────────────────────────────────────────────┘"
    
    echo -e "${CYAN}$status_msg${NC}" > /dev/tty1 2>/dev/null || true
    echo -e "${CYAN}$status_msg${NC}"
}

# Verificar se já foi executado
if [[ -f "$FIRST_BOOT_FLAG" ]]; then
    show_header
    log "Bootstrap já foi executado anteriormente."
    info "Para forçar nova execução, remova: $FIRST_BOOT_FLAG"
    exit 0
fi

# Mostrar header inicial
show_header

log "=== RASPBERRY PI BOOTSTRAP - NVIRTUAL ==="
log "Baixando e executando configuração automática do GitHub"

# Aguardar conectividade de rede
show_status "CONECTIVIDADE" "Verificando conexão com internet..."
log "Aguardando conectividade de rede..."

for i in {1..60}; do
    if ping -c 1 8.8.8.8 > /dev/null 2>&1; then
        log "Conectividade de rede estabelecida"
        break
    fi
    if [[ $i -eq 60 ]]; then
        error "Falha ao estabelecer conectividade após 60 tentativas (2 minutos)"
    fi
    show_status "AGUARDANDO REDE" "Tentativa $i/60 - Testando conexão..."
    sleep 2
done

# Aguardar estabilização da rede
show_status "REDE" "Estabilizando conexão..."
sleep 5

# Testar conectividade com GitHub
show_status "GITHUB" "Testando acesso ao repositório..."
if ! ping -c 3 github.com > /dev/null 2>&1; then
    error "Não foi possível acessar github.com"
fi

log "✅ Acesso ao GitHub confirmado"

# Baixar script principal do GitHub
show_status "DOWNLOAD" "Baixando script de configuração..."
log "Baixando script principal: $GITHUB_URL"

cd /tmp

# Remover arquivo anterior se existir
rm -f "$SCRIPT_NAME"

# Baixar com autenticação
if wget --header="Authorization: token $GITHUB_TOKEN" \
        --timeout=30 \
        --tries=3 \
        "$GITHUB_URL" \
        -O "$SCRIPT_NAME"; then
    log "✅ Script baixado com sucesso"
else
    error "❌ Falha ao baixar script do GitHub"
fi

# Verificar se o arquivo foi baixado corretamente
if [[ ! -f "$SCRIPT_NAME" ]] || [[ ! -s "$SCRIPT_NAME" ]]; then
    error "❌ Arquivo baixado está vazio ou corrompido"
fi

# Verificar se é um script bash válido
if ! head -1 "$SCRIPT_NAME" | grep -q "#!/bin/bash"; then
    error "❌ Arquivo baixado não é um script bash válido"
fi

# Tornar executável
chmod +x "$SCRIPT_NAME"
log "✅ Script preparado para execução"

# Mostrar informações do script baixado
info "Arquivo: $SCRIPT_NAME"
info "Tamanho: $(du -h "$SCRIPT_NAME" | cut -f1)"
info "Modificado: $(stat -c %y "$SCRIPT_NAME" | cut -d. -f1)"

# Executar script principal
show_status "EXECUTANDO" "Iniciando configuração automática..."
log "🚀 Executando script de configuração automática..."

# Função para executar script com output visível
execute_with_visual_output() {
    local script_name="$1"
    local log_file="/tmp/install_output.log"

    # Limpar log anterior
    > "$log_file"

    # Executar script em background e capturar PID
    ./"$script_name" > "$log_file" 2>&1 &
    local script_pid=$!

    # Mostrar progresso enquanto script executa
    local dots=""
    while kill -0 "$script_pid" 2>/dev/null; do
        # Mostrar últimas linhas do log na tela
        if [[ -f "$log_file" ]]; then
            local last_lines=$(tail -n 3 "$log_file" 2>/dev/null | grep -v "^$" | head -n 1)
            if [[ -n "$last_lines" ]]; then
                show_status "EXECUTANDO" "$last_lines"
                # Também mostrar na tela principal
                echo -e "${BLUE}[SCRIPT] $last_lines${NC}" > /dev/tty1 2>/dev/null || true
                echo -e "${BLUE}[SCRIPT] $last_lines${NC}"
            fi
        fi

        # Animação de progresso
        dots="${dots}."
        if [[ ${#dots} -gt 3 ]]; then
            dots=""
        fi
        show_status "PROCESSANDO" "Instalação em andamento$dots (PID: $script_pid)"
        sleep 3
    done

    # Aguardar conclusão e obter código de saída
    wait "$script_pid"
    local exit_code=$?

    # Mostrar últimas linhas do log
    if [[ -f "$log_file" ]]; then
        log "📋 Últimas mensagens do script:"
        tail -n 10 "$log_file" | while read -r line; do
            if [[ -n "$line" ]]; then
                echo -e "${CYAN}[LOG] $line${NC}" > /dev/tty1 2>/dev/null || true
                echo -e "${CYAN}[LOG] $line${NC}"
            fi
        done
    fi

    return $exit_code
}

# Executar o script baixado com output visível
if execute_with_visual_output "$SCRIPT_NAME"; then
    log "✅ Configuração automática concluída com sucesso!"
else
    warning "⚠️ Script executado com avisos. Verificando resultado..."
fi

# Verificar se a instalação foi bem-sucedida
show_status "VERIFICAÇÃO" "Verificando serviços instalados..."

# Verificar serviços principais
SERVICES_OK=true

if systemctl is-active --quiet zabbix-proxy 2>/dev/null; then
    log "✅ Zabbix Proxy está ativo"
else
    warning "⚠️ Zabbix Proxy não está ativo"
    SERVICES_OK=false
fi

if systemctl is-active --quiet zabbix-agent 2>/dev/null; then
    log "✅ Zabbix Agent está ativo"
else
    warning "⚠️ Zabbix Agent não está ativo"
    SERVICES_OK=false
fi

# Verificar arquivo de informações
INFO_FOUND=false
for dir in "/home/<USER>" "/home/<USER>" "/home/<USER>" "/home/<USER>" "/root"; do
    if [[ -f "$dir/SISTEMA_INFO.txt" ]]; then
        log "✅ Arquivo de informações encontrado: $dir/SISTEMA_INFO.txt"
        INFO_FOUND=true
        break
    fi
done

if [[ "$INFO_FOUND" == "false" ]]; then
    warning "⚠️ Arquivo de informações do sistema não encontrado"
fi

# Marcar bootstrap como concluído
log "Marcando bootstrap como concluído..."
echo "$(date): Bootstrap executado com sucesso" > "$FIRST_BOOT_FLAG"
echo "Script baixado de: $GITHUB_URL" >> "$FIRST_BOOT_FLAG"
echo "Versão do script: $(head -5 "$SCRIPT_NAME" | grep -E "(Data:|Version:|v[0-9])" || echo "Não identificada")" >> "$FIRST_BOOT_FLAG"
chmod 644 "$FIRST_BOOT_FLAG"

# Limpar arquivo temporário
rm -f "$SCRIPT_NAME"

# Mostrar tela final
show_final_screen() {
    clear > /dev/tty1 2>/dev/null || true
    local final_screen="
╔══════════════════════════════════════════════════════════════╗
║                    ✅ BOOTSTRAP CONCLUÍDO!                  ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  🎉 Configuração automática executada com sucesso!          ║
║                                                              ║
║  📥 Script baixado de: GitHub (versão mais atual)           ║
║  🔧 Zabbix Proxy + Agent instalados                         ║
║  📡 Tactical RMM configurado                                ║
║                                                              ║
║  🌐 Próximos passos:                                        ║
║  1. Acesse: monitora.nvirtual.com.br                       ║
║  2. Altere nome do proxy de 'MUDAR' para nome desejado     ║
║                                                              ║
║  📄 Informações: Verifique arquivo SISTEMA_INFO.txt        ║
║                                                              ║
║  Desenvolvido por: Paulo Matheus - NVirtual                ║
╚══════════════════════════════════════════════════════════════╝

Sistema será reiniciado em 30 segundos...
Pressione CTRL+C para cancelar reinicialização.
"
    echo -e "${GREEN}$final_screen${NC}" > /dev/tty1 2>/dev/null || true
    echo -e "${GREEN}$final_screen${NC}"
}

show_final_screen

# Aguardar e reiniciar
sleep 30
log "🔄 Reiniciando sistema para finalizar configuração..."
reboot
