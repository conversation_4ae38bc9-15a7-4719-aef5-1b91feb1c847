#!/bin/bash

# Script de configuração para primeiro boot - Raspberry Pi Plug and Play
# Instala automaticamente Zabbix Proxy e Tactical RMM
# Autor: <PERSON> Matheus
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Arquivo de flag para verificar se já foi executado
FIRST_BOOT_FLAG="/home/<USER>/.first_boot_completed"

# Verificar se já foi executado
if [[ -f "$FIRST_BOOT_FLAG" ]]; then
    log "Script de primeiro boot já foi executado anteriormente."
    exit 0
fi

log "=== RASPBERRY PI PLUG AND PLAY SETUP ==="
log "Iniciando configuração automática do primeiro boot..."
log "Desenvolvido por Paulo Matheus - NVirtual"

# Configurações padrão (plug and play)
ZABBIX_HOSTNAME="MUDAR"
TACTICAL_CLIENT_ID="1"
TACTICAL_CLIENT_FILIAL="1"

log "Configurações padrão aplicadas:"
info "• Zabbix Hostname: $ZABBIX_HOSTNAME"
info "• Tactical Client ID: $TACTICAL_CLIENT_ID"
info "• Tactical Site ID: $TACTICAL_CLIENT_FILIAL"

# Aguardar conectividade de rede
log "Aguardando conectividade de rede..."
for i in {1..30}; do
    if ping -c 1 ******* > /dev/null 2>&1; then
        log "✅ Conectividade de rede estabelecida"
        break
    fi
    if [[ $i -eq 30 ]]; then
        error "❌ Falha ao estabelecer conectividade de rede após 30 tentativas"
    fi
    sleep 2
done

# Aguardar um pouco mais para estabilizar a rede
sleep 10

# Baixar o script principal do GitHub
log "Baixando script de instalação do GitHub..."
cd /tmp

# Token e URL do GitHub
GITHUB_TOKEN="****************************************"
GITHUB_URL="https://raw.githubusercontent.com/nubium-cloud/Zabbix-Proxy-Instalation/refs/heads/main/install_zabbix_tactical_rmm.sh"

# Baixar com autenticação
if wget --header="Authorization: token $GITHUB_TOKEN" "$GITHUB_URL" -O install_zabbix_tactical_rmm.sh; then
    log "✅ Script baixado com sucesso"
    chmod +x install_zabbix_tactical_rmm.sh
else
    error "❌ Falha ao baixar o script do GitHub"
fi

# Criar versão modificada do script para execução automática
log "Preparando instalação automática..."

# Criar versão modificada do script que não requer interação
log "Modificando script para execução automática..."
cp install_zabbix_tactical_rmm.sh install_zabbix_tactical_rmm_auto.sh

# Modificar o script para usar valores padrão sem prompts
sed -i "s/read -p \"Digite o nome do Zabbix Proxy.*$/ZABBIX_HOSTNAME=\"$ZABBIX_HOSTNAME\"/" install_zabbix_tactical_rmm_auto.sh
sed -i "s/read -p \"Digite o ID do Cliente.*$/TACTICAL_CLIENT_ID=\"$TACTICAL_CLIENT_ID\"/" install_zabbix_tactical_rmm_auto.sh
sed -i "s/read -p \"Digite a Filial do Cliente.*$/TACTICAL_CLIENT_FILIAL=\"$TACTICAL_CLIENT_FILIAL\"/" install_zabbix_tactical_rmm_auto.sh

# Remover verificações de entrada vazia
sed -i '/if \[\[ -z "\$ZABBIX_HOSTNAME" \]\]; then/,+2d' install_zabbix_tactical_rmm_auto.sh
sed -i '/if \[\[ -z "\$TACTICAL_CLIENT_ID" \]\]; then/,+2d' install_zabbix_tactical_rmm_auto.sh
sed -i '/if \[\[ -z "\$TACTICAL_CLIENT_FILIAL" \]\]; then/,+2d' install_zabbix_tactical_rmm_auto.sh

# Configurar para aceitar automaticamente o IP proposto
sed -i 's/read -p "Confirma a configuração do IP fixo.*$/CONFIRM_IP="s"/' install_zabbix_tactical_rmm_auto.sh
sed -i 's/read -p "Deseja usar um IP fixo diferente.*$/USE_DIFFERENT_IP="N"/' install_zabbix_tactical_rmm_auto.sh
sed -i 's/read -p "⚠️  Deseja continuar com a configuração de IP fixo.*$/CONFIRM_SSH_RISK="s"/' install_zabbix_tactical_rmm_auto.sh

chmod +x install_zabbix_tactical_rmm_auto.sh

log "Iniciando instalação automática do Zabbix e Tactical RMM..."

# Executar o script modificado
if ./install_zabbix_tactical_rmm_auto.sh; then
    log "✅ Instalação concluída com sucesso!"
else
    warning "⚠️ Instalação concluída com avisos. Verificando serviços..."
fi

# Verificar se os serviços estão rodando
log "Verificando status dos serviços instalados..."

# Verificar Zabbix Proxy
if systemctl is-active --quiet zabbix-proxy; then
    log "✅ Zabbix Proxy está ativo"
else
    warning "⚠️ Zabbix Proxy não está ativo. Tentando iniciar..."
    systemctl start zabbix-proxy || warning "Falha ao iniciar Zabbix Proxy"
fi

# Verificar Zabbix Agent
if systemctl is-active --quiet zabbix-agent; then
    log "✅ Zabbix Agent está ativo"
else
    warning "⚠️ Zabbix Agent não está ativo. Tentando iniciar..."
    systemctl start zabbix-agent || warning "Falha ao iniciar Zabbix Agent"
fi

# Criar arquivo de informações do sistema
log "Criando arquivo de informações do sistema..."
cat > /home/<USER>/SISTEMA_INFO.txt << EOF
=== RASPBERRY PI CONFIGURADO AUTOMATICAMENTE ===
Data da configuração: $(date)
Desenvolvido por: Paulo Matheus - NVirtual

=== CONFIGURAÇÕES APLICADAS ===
Zabbix Hostname: $ZABBIX_HOSTNAME
Tactical Client ID: $TACTICAL_CLIENT_ID
Tactical Site ID: $TACTICAL_CLIENT_FILIAL

=== PRÓXIMOS PASSOS ===
1. Acesse o Zabbix Server (monitora.nvirtual.com.br)
2. Altere o nome do proxy de "$ZABBIX_HOSTNAME" para o nome desejado
3. O sistema já está monitorado pelo Tactical RMM

=== INFORMAÇÕES TÉCNICAS ===
IP do sistema: $(hostname -I | awk '{print $1}')
Hostname: $(hostname)
Zabbix Server: monitora.nvirtual.com.br
Tactical Mesh: mesh.centralmesh.nvirtual.com.br
Tactical API: api.centralmesh.nvirtual.com.br

=== COMANDOS ÚTEIS ===
Verificar status Zabbix Proxy: sudo systemctl status zabbix-proxy
Verificar status Zabbix Agent: sudo systemctl status zabbix-agent
Logs Zabbix Proxy: sudo tail -f /var/log/zabbix/zabbix_proxy.log
Logs Zabbix Agent: sudo tail -f /var/log/zabbix/zabbix_agentd.log

=== ALTERAÇÃO DO HOSTNAME ZABBIX ===
Para alterar o hostname do Zabbix Proxy:
1. sudo nano /etc/zabbix/zabbix_proxy.conf
2. Alterar linha: Hostname=NOVO_NOME
3. sudo systemctl restart zabbix-proxy

EOF

chown pi:pi /home/<USER>/SISTEMA_INFO.txt

# Marcar como concluído
touch "$FIRST_BOOT_FLAG"
chown pi:pi "$FIRST_BOOT_FLAG"

# Limpar arquivos temporários
rm -f /tmp/install_zabbix_tactical_rmm.sh
rm -f /tmp/install_zabbix_tactical_rmm_auto.sh

log "=== CONFIGURAÇÃO CONCLUÍDA ==="
log "✅ Sistema configurado com sucesso!"
log "📄 Informações salvas em: /home/<USER>/SISTEMA_INFO.txt"
log "🔧 Para alterar o hostname Zabbix, edite: /etc/zabbix/zabbix_proxy.conf"
log "🌐 Acesse o Zabbix em: monitora.nvirtual.com.br"

# Opcional: Reiniciar para aplicar todas as configurações
read -t 30 -p "Deseja reiniciar o sistema agora para finalizar? (s/N): " RESTART_CHOICE
if [[ "$RESTART_CHOICE" =~ ^[Ss]$ ]]; then
    log "Reiniciando sistema em 10 segundos..."
    sleep 10
    reboot
else
    log "Sistema configurado. Reinicie manualmente quando conveniente."
fi
