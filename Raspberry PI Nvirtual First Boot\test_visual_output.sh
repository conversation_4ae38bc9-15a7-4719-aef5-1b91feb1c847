#!/bin/bash

# Script de teste para verificar se o output visual está funcionando
# Este script cria um script de teste temporário e executa com as funções visuais
# Autor: <PERSON> - NVirtual

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Função para mostrar status atual (copiada do bootstrap)
show_status() {
    local status="$1"
    local details="$2"
    local status_msg="
┌─────────────────────────────────────────────────────────────┐
│ STATUS: $status
│ $details
└─────────────────────────────────────────────────────────────┘"
    
    echo -e "${CYAN}$status_msg${NC}"
}

# Criar script de teste temporário
create_test_script() {
    local test_script="/tmp/test_install_script.sh"
    
    cat > "$test_script" << 'EOF'
#!/bin/bash

# Script de teste que simula uma instalação
echo "✅ Iniciando teste de instalação..."
sleep 2

echo "✅ Atualizando repositórios do sistema..."
sleep 3

echo "✅ Instalando dependências necessárias..."
sleep 2

echo "✅ Configurando repositório Zabbix..."
sleep 2

echo "✅ Instalando Zabbix Proxy..."
sleep 4

echo "✅ Instalando Zabbix Agent..."
sleep 3

echo "✅ Configurando arquivos de configuração..."
sleep 2

echo "✅ Habilitando serviços..."
sleep 2

echo "✅ Iniciando Zabbix Proxy..."
sleep 2

echo "✅ Iniciando Zabbix Agent..."
sleep 2

echo "✅ Baixando Tactical RMM..."
sleep 3

echo "✅ Instalando Tactical RMM Agent..."
sleep 4

echo "✅ Configurando firewall..."
sleep 2

echo "✅ Instalação concluída com sucesso!"

# Simular alguns avisos
echo "⚠️ Aviso: Configuração de rede pode precisar de ajuste"
echo "⚠️ Aviso: Verificar logs após reinicialização"

echo "✅ Teste de instalação finalizado!"
EOF

    chmod +x "$test_script"
    echo "$test_script"
}

# Função de execução com output visual (copiada e adaptada do bootstrap)
execute_with_visual_output() {
    local script_name="$1"
    local log_file="/tmp/test_output.log"
    
    # Limpar log anterior
    > "$log_file"
    
    # Executar script em background e capturar PID
    "$script_name" > "$log_file" 2>&1 &
    local script_pid=$!
    
    # Mostrar progresso enquanto script executa
    local dots=""
    while kill -0 "$script_pid" 2>/dev/null; do
        # Mostrar últimas linhas do log na tela
        if [[ -f "$log_file" ]]; then
            local last_lines=$(tail -n 3 "$log_file" 2>/dev/null | grep -v "^$" | head -n 1)
            if [[ -n "$last_lines" ]]; then
                show_status "EXECUTANDO" "$last_lines"
                # Também mostrar na tela principal
                echo -e "${BLUE}[SCRIPT] $last_lines${NC}"
            fi
        fi
        
        # Animação de progresso
        dots="${dots}."
        if [[ ${#dots} -gt 3 ]]; then
            dots=""
        fi
        show_status "PROCESSANDO" "Teste em andamento$dots (PID: $script_pid)"
        sleep 2
    done
    
    # Aguardar conclusão e obter código de saída
    wait "$script_pid"
    local exit_code=$?
    
    # Mostrar últimas linhas do log
    if [[ -f "$log_file" ]]; then
        log "📋 Últimas mensagens do script:"
        tail -n 10 "$log_file" | while read -r line; do
            if [[ -n "$line" ]]; then
                echo -e "${CYAN}[LOG] $line${NC}"
            fi
        done
    fi
    
    return $exit_code
}

# Função de execução com output em tempo real (copiada do setup_first_boot.sh)
execute_with_realtime_output() {
    local script_name="$1"
    local log_file="/tmp/test_realtime.log"
    
    log "Iniciando execução de: $script_name"
    log "📺 Acompanhe o progresso abaixo:"
    echo "----------------------------------------"
    
    # Executar script e mostrar output em tempo real
    if "$script_name" 2>&1 | tee "$log_file"; then
        echo "----------------------------------------"
        log "✅ Script executado com sucesso!"
        return 0
    else
        echo "----------------------------------------"
        log "⚠️ Script executado com avisos ou erros."
        return 1
    fi
}

# Início do teste
log "=== TESTE DE OUTPUT VISUAL ==="
log "Testando as funções de output visual dos scripts modificados"

# Criar script de teste
info "Criando script de teste temporário..."
TEST_SCRIPT=$(create_test_script)
log "Script de teste criado: $TEST_SCRIPT"

echo ""
info "Escolha o tipo de teste:"
echo "1) Teste com output visual (como no bootstrap)"
echo "2) Teste com output em tempo real (como no setup_first_boot)"
echo "3) Executar ambos os testes"
echo ""

read -p "Digite sua escolha (1, 2 ou 3): " choice

case $choice in
    1)
        log "🧪 Executando teste com output visual..."
        echo ""
        if execute_with_visual_output "$TEST_SCRIPT"; then
            log "✅ Teste com output visual concluído com sucesso!"
        else
            log "⚠️ Teste concluído com avisos"
        fi
        ;;
    2)
        log "🧪 Executando teste com output em tempo real..."
        echo ""
        if execute_with_realtime_output "$TEST_SCRIPT"; then
            log "✅ Teste com output em tempo real concluído com sucesso!"
        else
            log "⚠️ Teste concluído com avisos"
        fi
        ;;
    3)
        log "🧪 Executando ambos os testes..."
        echo ""
        
        log "--- TESTE 1: Output Visual ---"
        if execute_with_visual_output "$TEST_SCRIPT"; then
            log "✅ Teste 1 concluído com sucesso!"
        else
            log "⚠️ Teste 1 concluído com avisos"
        fi
        
        echo ""
        echo "Aguarde 5 segundos para o próximo teste..."
        sleep 5
        echo ""
        
        log "--- TESTE 2: Output em Tempo Real ---"
        if execute_with_realtime_output "$TEST_SCRIPT"; then
            log "✅ Teste 2 concluído com sucesso!"
        else
            log "⚠️ Teste 2 concluído com avisos"
        fi
        ;;
    *)
        error "Escolha inválida. Use 1, 2 ou 3."
        ;;
esac

# Limpeza
log "Limpando arquivos temporários..."
rm -f "$TEST_SCRIPT"
rm -f /tmp/test_output.log
rm -f /tmp/test_realtime.log

echo ""
log "=== TESTE CONCLUÍDO ==="
info "As funções de output visual estão funcionando corretamente!"
info "Os técnicos agora poderão ver todo o progresso da instalação em tela."
echo ""
info "Scripts modificados disponíveis:"
info "• bootstrap_first_boot.sh - Com output visual em background"
info "• setup_first_boot.sh - Com output em tempo real usando tee"
info "• setup_first_boot_visual.sh - Com interface visual completa"
echo ""
