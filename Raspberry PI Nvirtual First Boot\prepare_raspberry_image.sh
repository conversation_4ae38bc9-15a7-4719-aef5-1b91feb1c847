#!/bin/bash

# Script para preparar imagem do Raspberry Pi com instalação automática
# Este script deve ser executado em um Raspberry Pi limpo para criar a imagem base
# Autor: Paulo Matheus
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

log "=== PREPARADOR DE IMAGEM RASPBERRY PI PLUG AND PLAY ==="
log "Configurando Raspberry Pi para instalação automática no primeiro boot"
log "Desenvolvido por Paulo Matheus - NVirtual"

# Verificar se está rodando como root
if [[ $EUID -ne 0 ]]; then
   error "Este script deve ser executado como root (sudo)"
fi

# Verificar se está em um Raspberry Pi
if ! grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    warning "⚠️ Este script foi desenvolvido para Raspberry Pi"
    read -p "Deseja continuar mesmo assim? (s/N): " CONTINUE
    if [[ ! "$CONTINUE" =~ ^[Ss]$ ]]; then
        exit 0
    fi
fi

# Verificar arquivos necessários
log "Verificando arquivos necessários..."
REQUIRED_FILES=("setup_first_boot.sh" "install_first_boot_service.sh")
for file in "${REQUIRED_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        error "Arquivo necessário não encontrado: $file"
    fi
done

log "✅ Todos os arquivos necessários encontrados"

# Atualizar sistema base
log "Atualizando sistema base..."
apt-get update -y
apt-get upgrade -y

# Instalar dependências básicas
log "Instalando dependências básicas..."
apt-get install -y curl wget git vim nano htop

# Configurar timezone para Brasil
log "Configurando timezone..."
timedatectl set-timezone America/Sao_Paulo

# Habilitar SSH se não estiver habilitado
log "Verificando SSH..."
if ! systemctl is-enabled ssh > /dev/null 2>&1; then
    log "Habilitando SSH..."
    systemctl enable ssh
    systemctl start ssh
else
    log "✅ SSH já está habilitado"
fi

# Executar o instalador do serviço de primeiro boot
log "Configurando serviço de primeiro boot..."
chmod +x install_first_boot_service.sh
./install_first_boot_service.sh

# Configurar hostname padrão
log "Configurando hostname padrão..."
echo "raspberrypi-zabbix" > /etc/hostname
sed -i 's/*********.*/*********\traspberrypi-zabbix/' /etc/hosts

# Criar arquivo de identificação da imagem
log "Criando arquivo de identificação..."
cat > /home/<USER>/IMAGEM_INFO.txt << EOF
=== RASPBERRY PI PLUG AND PLAY - INFORMAÇÕES DA IMAGEM ===

Data de criação: $(date)
Versão do sistema: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
Desenvolvido por: Paulo Matheus - NVirtual

=== CARACTERÍSTICAS DESTA IMAGEM ===
✅ Sistema base atualizado
✅ SSH habilitado
✅ Timezone configurado (America/Sao_Paulo)
✅ Serviço de primeiro boot configurado
✅ Dependências básicas instaladas

=== CONFIGURAÇÃO AUTOMÁTICA NO PRIMEIRO BOOT ===
- Zabbix Proxy (hostname: "MUDAR")
- Zabbix Agent
- Tactical RMM (Client ID: 1, Site ID: 1)
- Firewall básico
- Configuração de rede

=== REQUISITOS PARA USO ===
1. Conexão com internet no primeiro boot
2. Aguardar 10-15 minutos para instalação completa
3. Verificar arquivo /home/<USER>/SISTEMA_INFO.txt após instalação

=== PRÓXIMOS PASSOS APÓS PRIMEIRO BOOT ===
1. Acessar Zabbix Server: monitora.nvirtual.com.br
2. Alterar nome do proxy de "MUDAR" para nome desejado
3. Sistema estará automaticamente no Tactical RMM

=== COMANDOS ÚTEIS ===
check-setup                    # Verificar status da instalação
sudo journalctl -u first-boot-setup.service -f  # Ver logs da instalação

=== SUPORTE ===
Desenvolvido por: Paulo Matheus - NVirtual
EOF

chown pi:pi /home/<USER>/IMAGEM_INFO.txt

# Configurar mensagem de boas-vindas
log "Configurando mensagem de boas-vindas..."
cat > /etc/motd << 'EOF'

 ____                 _                          ____  _ 
|  _ \ __ _ ___ _ __   | |__   ___ _ __ _ __ _   _ |  _ \(_)
| |_) / _` / __| '_ \  | '_ \ / _ \ '__| '__| | | || |_) | |
|  _ < (_| \__ \ |_) | | |_) |  __/ |  | |  | |_| ||  __/| |
|_| \_\__,_|___/ .__/  |_.__/ \___|_|  |_|   \__, ||_|   |_|
               |_|                          |___/          

=== RASPBERRY PI PLUG AND PLAY ===
Desenvolvido por Paulo Matheus - NVirtual

🚀 INSTALAÇÃO AUTOMÁTICA NO PRIMEIRO BOOT
   - Zabbix Proxy + Agent
   - Tactical RMM Agent
   - Configuração de rede e firewall

📋 VERIFICAR STATUS: check-setup
📄 INFORMAÇÕES: cat /home/<USER>/IMAGEM_INFO.txt

⚠️  IMPORTANTE: Certifique-se de ter conexão com internet!

EOF

# Limpar histórico e logs para imagem limpa
log "Limpando sistema para imagem..."
history -c
> /home/<USER>/.bash_history
journalctl --vacuum-time=1d
apt-get autoremove -y
apt-get autoclean

# Remover arquivos de instalação
log "Removendo arquivos de instalação..."
rm -f install_first_boot_service.sh

log "✅ Preparação da imagem concluída com sucesso!"
echo
echo "=========================================="
echo "IMAGEM RASPBERRY PI PLUG AND PLAY PRONTA"
echo "=========================================="
echo
info "🎯 A imagem está pronta para ser clonada!"
echo
info "📋 O que foi configurado:"
info "• Sistema base atualizado"
info "• SSH habilitado"
info "• Timezone Brasil configurado"
info "• Serviço de primeiro boot instalado"
info "• Mensagens informativas criadas"
echo
warning "⚠️  PRÓXIMOS PASSOS:"
warning "1. Desligue o Raspberry Pi: sudo shutdown -h now"
warning "2. Clone o SD card para criar a imagem base"
warning "3. Use esta imagem para novos deployments"
echo
info "🔧 COMO USAR A IMAGEM:"
info "1. Grave a imagem em um novo SD card"
info "2. Insira no Raspberry Pi e ligue"
info "3. Conecte à internet"
info "4. Aguarde 10-15 minutos para instalação automática"
info "5. Acesse monitora.nvirtual.com.br e altere nome do proxy"
echo
log "🎉 Imagem plug-and-play criada com sucesso!"
