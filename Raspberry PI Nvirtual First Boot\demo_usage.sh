#!/bin/bash

# Script de demonstração - Como usar a solução Raspberry Pi Plug and Play
# Este script mostra o fluxo completo de uso
# Autor: <PERSON>
# Data: $(date +%Y-%m-%d)

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[INFO] $1${NC}"
    echo -e "${GREEN}[INFO] $1${NC}" > /dev/tty1 2>/dev/null || true
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
    echo -e "${YELLOW}[AVISO] $1${NC}" > /dev/tty1 2>/dev/null || true
}

info() {
    echo -e "${BLUE}[DEMO] $1${NC}"
    echo -e "${BLUE}[DEMO] $1${NC}" > /dev/tty1 2>/dev/null || true
}

echo "=========================================="
echo "RASPBERRY PI PLUG AND PLAY - DEMONSTRAÇÃO"
echo "=========================================="
echo
log "Desenvolvido por Paulo Matheus - NVirtual"
echo

info "Este script demonstra como usar a solução completa."
echo

echo "🎯 OBJETIVO:"
echo "Criar uma imagem de Raspberry Pi que instala automaticamente:"
echo "• Zabbix Proxy (hostname: 'MUDAR')"
echo "• Zabbix Agent"
echo "• Tactical RMM (Client ID: 1, Site ID: 1)"
echo

echo "📋 FLUXO COMPLETO:"
echo

echo "1️⃣  PREPARAÇÃO DA IMAGEM BASE"
echo "   Em um Raspberry Pi limpo:"
echo "   $ sudo ./prepare_raspberry_image.sh"
echo

echo "2️⃣  CRIAÇÃO DA IMAGEM"
echo "   $ sudo shutdown -h now"
echo "   # Clone o SD card com Raspberry Pi Imager ou dd"
echo

echo "3️⃣  USO DA IMAGEM"
echo "   • Grave a imagem em novo SD card"
echo "   • Insira no Raspberry Pi"
echo "   • Conecte à internet"
echo "   • Ligue o sistema"
echo "   • Aguarde 10-15 minutos"
echo

echo "4️⃣  VERIFICAÇÃO"
echo "   $ check-setup"
echo "   $ cat /home/<USER>/SISTEMA_INFO.txt"
echo

echo "5️⃣  CONFIGURAÇÃO FINAL"
echo "   • Acesse monitora.nvirtual.com.br"
echo "   • Altere nome do proxy de 'MUDAR' para nome desejado"
echo

echo "=========================================="
echo "ARQUIVOS NECESSÁRIOS"
echo "=========================================="
echo

REQUIRED_FILES=(
    "setup_first_boot.sh"
    "install_first_boot_service.sh"
    "prepare_raspberry_image.sh"
    "README_RASPBERRY_AUTOMATION.md"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        log "✅ $file"
    else
        warning "❌ $file (FALTANDO)"
    fi
done

echo
echo "=========================================="
echo "COMANDOS ÚTEIS APÓS INSTALAÇÃO"
echo "=========================================="
echo

echo "# Verificar status da instalação automática"
echo "check-setup"
echo

echo "# Ver logs da instalação em tempo real"
echo "sudo journalctl -u first-boot-setup.service -f"
echo

echo "# Ver informações do sistema configurado"
echo "cat /home/<USER>/SISTEMA_INFO.txt"
echo

echo "# Verificar serviços Zabbix"
echo "sudo systemctl status zabbix-proxy"
echo "sudo systemctl status zabbix-agent"
echo

echo "# Ver logs do Zabbix"
echo "sudo tail -f /var/log/zabbix/zabbix_proxy.log"
echo

echo "# Alterar hostname do Zabbix Proxy"
echo "sudo nano /etc/zabbix/zabbix_proxy.conf"
echo "# Alterar linha: Hostname=NOVO_NOME"
echo "sudo systemctl restart zabbix-proxy"
echo

echo "=========================================="
echo "CONFIGURAÇÕES PADRÃO"
echo "=========================================="
echo

echo "Zabbix Hostname: MUDAR"
echo "Tactical Client ID: 1"
echo "Tactical Site ID: 1"
echo "Zabbix Server: monitora.nvirtual.com.br"
echo "Tactical Mesh: mesh.centralmesh.nvirtual.com.br"
echo "Tactical API: api.centralmesh.nvirtual.com.br"
echo

echo "=========================================="
echo "EXEMPLO DE USO PRÁTICO"
echo "=========================================="
echo

info "CENÁRIO: Você quer instalar 10 Raspberry Pi em clientes diferentes"
echo

echo "1. Prepare UMA imagem base:"
echo "   $ sudo ./prepare_raspberry_image.sh"
echo

echo "2. Clone a imagem para 10 SD cards"
echo

echo "3. Para cada cliente:"
echo "   • Insira SD card no Raspberry Pi"
echo "   • Conecte à internet"
echo "   • Ligue o equipamento"
echo "   • Aguarde instalação automática"
echo "   • Acesse Zabbix e altere nome do proxy"
echo

echo "4. Resultado:"
echo "   • 10 sistemas monitorados automaticamente"
echo "   • Todos no Tactical RMM"
echo "   • Configuração mínima necessária"
echo

echo "=========================================="
echo "TROUBLESHOOTING"
echo "=========================================="
echo

echo "❓ Instalação não iniciou?"
echo "   $ sudo systemctl start first-boot-setup.service"
echo

echo "❓ Sem conectividade?"
echo "   $ ping -c 3 *******"
echo "   $ ip addr show"
echo

echo "❓ Serviços Zabbix não funcionam?"
echo "   $ sudo journalctl -u zabbix-proxy -f"
echo "   $ sudo systemctl restart zabbix-proxy"
echo

echo "❓ Verificar se instalação foi concluída?"
echo "   $ ls -la /home/<USER>/.first_boot_completed"
echo

echo
warning "⚠️  IMPORTANTE:"
warning "• Certifique-se de ter conexão com internet no primeiro boot"
warning "• A instalação pode levar até 15 minutos"
warning "• Não desligue durante a instalação automática"
echo

log "🎉 Solução Raspberry Pi Plug and Play pronta para uso!"
echo
echo "Para mais informações, consulte: README_RASPBERRY_AUTOMATION.md"
