#!/bin/bash

# Script para instalar serviço bootstrap no Raspberry Pi
# Este é o ÚNICO script que fica na imagem - o resto vem do GitHub
# Autor: Paulo Matheus - NVirtual
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}" > /dev/tty1 2>/dev/null || true
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    echo -e "${RED}[ERRO] $1${NC}" > /dev/tty1 2>/dev/null || true
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
    echo -e "${YELLOW}[AVISO] $1${NC}" > /dev/tty1 2>/dev/null || true
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
    echo -e "${BLUE}[INFO] $1${NC}" > /dev/tty1 2>/dev/null || true
}

log "=== INSTALADOR DO SERVIÇO BOOTSTRAP ==="
log "Configurando bootstrap para baixar scripts do GitHub no primeiro boot"

# Verificar se está rodando como root
if [[ $EUID -ne 0 ]]; then
   error "Este script deve ser executado como root (sudo)"
fi

# Verificar se o script bootstrap existe
if [[ ! -f "bootstrap_first_boot.sh" ]]; then
    error "Arquivo bootstrap_first_boot.sh não encontrado no diretório atual"
fi

# Copiar o script bootstrap para o local apropriado
log "Instalando script bootstrap..."
cp bootstrap_first_boot.sh /usr/local/bin/
chmod +x /usr/local/bin/bootstrap_first_boot.sh

# Criar o arquivo de serviço systemd
log "Criando serviço systemd bootstrap..."
cat > /etc/systemd/system/raspberry-bootstrap.service << 'EOF'
[Unit]
Description=Raspberry Pi Bootstrap - Download and Execute First Boot Configuration
After=network-online.target
Wants=network-online.target
DefaultDependencies=false

[Service]
Type=oneshot
ExecStart=/usr/local/bin/bootstrap_first_boot.sh
StandardOutput=tty
StandardError=tty
TTYPath=/dev/tty1
TimeoutStartSec=1800
RemainAfterExit=yes
User=root
Environment=TERM=linux

[Install]
WantedBy=multi-user.target
EOF

# Habilitar o serviço
log "Habilitando serviço bootstrap..."
systemctl daemon-reload
systemctl enable raspberry-bootstrap.service

# Criar script de verificação
log "Criando script de verificação..."
cat > /usr/local/bin/check_bootstrap_status.sh << 'EOF'
#!/bin/bash

# Script para verificar status do bootstrap

GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

clear
echo -e "${BLUE}
╔══════════════════════════════════════════════════════════════╗
║              STATUS DO BOOTSTRAP RASPBERRY PI               ║
╚══════════════════════════════════════════════════════════════╝${NC}"
echo

# Verificar se o serviço está habilitado
if systemctl is-enabled raspberry-bootstrap.service > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Serviço bootstrap está habilitado${NC}"
    
    # Verificar se já foi executado
    if [[ -f "/var/lib/first_boot_completed" ]]; then
        echo -e "${GREEN}✅ Bootstrap foi executado com sucesso${NC}"
        echo
        echo -e "${BLUE}=== INFORMAÇÕES DO BOOTSTRAP ===${NC}"
        cat /var/lib/first_boot_completed
        echo
        
        # Verificar serviços instalados
        echo -e "${BLUE}=== STATUS DOS SERVIÇOS ===${NC}"
        
        if systemctl is-active --quiet zabbix-proxy 2>/dev/null; then
            echo -e "${GREEN}✅ Zabbix Proxy está ativo${NC}"
        else
            echo -e "${RED}❌ Zabbix Proxy não está ativo${NC}"
        fi
        
        if systemctl is-active --quiet zabbix-agent 2>/dev/null; then
            echo -e "${GREEN}✅ Zabbix Agent está ativo${NC}"
        else
            echo -e "${RED}❌ Zabbix Agent não está ativo${NC}"
        fi
        
        # Procurar arquivo de informações
        echo
        echo -e "${BLUE}=== ARQUIVO DE INFORMAÇÕES ===${NC}"
        INFO_FOUND=false
        for dir in "/home/<USER>" "/home/<USER>" "/home/<USER>" "/root"; do
            if [[ -f "$dir/SISTEMA_INFO.txt" ]]; then
                echo -e "${GREEN}📄 Encontrado: $dir/SISTEMA_INFO.txt${NC}"
                INFO_FOUND=true
                break
            fi
        done
        
        if [[ "$INFO_FOUND" == "false" ]]; then
            echo -e "${YELLOW}⚠️ Arquivo de informações não encontrado${NC}"
        fi
        
    else
        echo -e "${YELLOW}⏳ Bootstrap ainda não foi executado${NC}"
        echo "O bootstrap será executado automaticamente no próximo boot"
        echo "Para executar manualmente: sudo systemctl start raspberry-bootstrap.service"
    fi
else
    echo -e "${RED}❌ Serviço bootstrap não está habilitado${NC}"
fi

echo
echo -e "${BLUE}=== COMANDOS ÚTEIS ===${NC}"
echo "Ver logs do bootstrap: sudo journalctl -u raspberry-bootstrap.service -f"
echo "Executar bootstrap manualmente: sudo systemctl start raspberry-bootstrap.service"
echo "Forçar nova execução: sudo rm /var/lib/first_boot_completed && sudo systemctl start raspberry-bootstrap.service"
echo "Ver informações do sistema: cat /home/<USER>/SISTEMA_INFO.txt"
EOF

chmod +x /usr/local/bin/check_bootstrap_status.sh

# Criar arquivo de informações da imagem
log "Criando arquivo de informações da imagem..."
cat > /home/<USER>/IMAGEM_BOOTSTRAP_INFO.txt << EOF
=== RASPBERRY PI BOOTSTRAP - INFORMAÇÕES DA IMAGEM ===

Data de criação: $(date)
Versão do sistema: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
Desenvolvido por: Paulo Matheus - NVirtual

=== CARACTERÍSTICAS DESTA IMAGEM BOOTSTRAP ===
✅ Sistema base atualizado
✅ SSH habilitado
✅ Serviço bootstrap configurado
✅ Scripts baixados automaticamente do GitHub

=== VANTAGENS DO BOOTSTRAP ===
🔄 Sempre usa a versão mais atual dos scripts
📥 Download automático do GitHub no primeiro boot
🚀 Não precisa recriar imagem para atualizações
🔧 Configuração sempre atualizada

=== REPOSITÓRIO GITHUB ===
Repositório: nubium-cloud/Raspberry-First-Boot-Configuration
Script principal: setup_first_boot.sh
URL: https://github.com/nubium-cloud/Raspberry-First-Boot-Configuration

=== O QUE ACONTECE NO PRIMEIRO BOOT ===
1. 🌐 Aguarda conectividade de rede
2. 📥 Baixa script mais atual do GitHub
3. 🔧 Executa configuração automática
4. ✅ Instala Zabbix + Tactical RMM
5. 🔄 Reinicia sistema

=== REQUISITOS ===
- Conexão com internet no primeiro boot
- Acesso ao GitHub
- Aproximadamente 10-15 minutos

=== COMANDOS ÚTEIS ===
check-bootstrap               # Verificar status do bootstrap
sudo journalctl -u raspberry-bootstrap.service -f  # Ver logs

=== DESENVOLVIDO POR ===
Paulo Matheus - NVirtual
EOF

# Tentar criar para usuário suportenv se existir
if [[ -d "/home/<USER>" ]]; then
    chown suportenv:suportenv /home/<USER>/IMAGEM_BOOTSTRAP_INFO.txt 2>/dev/null || true
fi

# Criar alias para verificação
for user_dir in /home/<USER>/home/<USER>/home/<USER>
    if [[ -d "$user_dir" ]] && [[ -f "$user_dir/.bashrc" ]]; then
        echo "alias check-bootstrap='sudo /usr/local/bin/check_bootstrap_status.sh'" >> "$user_dir/.bashrc"
    fi
done

log "✅ Serviço bootstrap instalado com sucesso!"
echo
echo "=========================================="
echo "SERVIÇO BOOTSTRAP CONFIGURADO"
echo "=========================================="
echo
info "📋 O que foi configurado:"
info "• Serviço systemd: raspberry-bootstrap.service"
info "• Script bootstrap: /usr/local/bin/bootstrap_first_boot.sh"
info "• Script de verificação: /usr/local/bin/check_bootstrap_status.sh"
info "• Arquivo de informações: /home/<USER>/IMAGEM_BOOTSTRAP_INFO.txt"
echo
info "🔄 VANTAGENS DO BOOTSTRAP:"
info "• Scripts sempre atualizados do GitHub"
info "• Não precisa recriar imagem para updates"
info "• Configuração sempre na versão mais recente"
info "• Facilita manutenção e atualizações"
echo
info "🔧 Comandos úteis:"
info "• Verificar status: check-bootstrap"
info "• Ver logs: sudo journalctl -u raspberry-bootstrap.service -f"
info "• Executar manualmente: sudo systemctl start raspberry-bootstrap.service"
echo
warning "⚠️  IMPORTANTE:"
warning "• O bootstrap baixa scripts do GitHub no primeiro boot"
warning "• Certifique-se de ter conexão com internet"
warning "• Token do GitHub deve estar válido"
echo
log "🎯 Imagem bootstrap pronta para uso!"
echo
info "📝 PRÓXIMOS PASSOS:"
info "1. Desligue o Raspberry Pi: sudo shutdown -h now"
info "2. Clone o SD card para criar imagem bootstrap"
info "3. Esta imagem sempre usará scripts atualizados do GitHub!"
