#!/bin/bash

# Script para configurar usuário suportenv no Raspberry Pi
# Usado pela NVirtual como usuário padrão
# Autor: <PERSON>
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

log "=== CONFIGURADOR DO USUÁRIO SUPORTENV ==="
log "Configurando usuário padrão da NVirtual para Raspberry Pi"

# Verificar se está rodando como root
if [[ $EUID -ne 0 ]]; then
   error "Este script deve ser executado como root (sudo)"
fi

# Verificar se usuário suportenv já existe
if id "suportenv" &>/dev/null; then
    log "✅ Usuário 'suportenv' já existe"
    info "UID: $(id -u suportenv)"
    info "Grupos: $(groups suportenv | cut -d: -f2)"
    info "Home: $(eval echo ~suportenv)"
else
    log "Criando usuário 'suportenv'..."
    
    # Criar usuário com diretório home
    useradd -m -s /bin/bash suportenv
    
    if id "suportenv" &>/dev/null; then
        log "✅ Usuário 'suportenv' criado com sucesso"
    else
        error "❌ Falha ao criar usuário 'suportenv'"
    fi
fi

# Adicionar ao grupo sudo se existir
if getent group sudo > /dev/null 2>&1; then
    if groups suportenv | grep -q sudo; then
        log "✅ Usuário 'suportenv' já está no grupo sudo"
    else
        log "Adicionando 'suportenv' ao grupo sudo..."
        usermod -aG sudo suportenv
        log "✅ Usuário 'suportenv' adicionado ao grupo sudo"
    fi
else
    warning "Grupo 'sudo' não existe neste sistema"
fi

# Configurar senha padrão (opcional - remover em produção)
read -p "Deseja definir uma senha para o usuário suportenv? (s/N): " SET_PASSWORD
if [[ "$SET_PASSWORD" =~ ^[Ss]$ ]]; then
    echo "Digite a senha para o usuário suportenv:"
    passwd suportenv
    log "✅ Senha definida para usuário 'suportenv'"
else
    warning "Usuário 'suportenv' criado sem senha"
    info "Para definir senha depois: sudo passwd suportenv"
fi

# Configurar SSH para o usuário
log "Configurando SSH para usuário 'suportenv'..."
SUPORTENV_HOME="/home/<USER>"

# Criar diretório .ssh se não existir
if [[ ! -d "$SUPORTENV_HOME/.ssh" ]]; then
    mkdir -p "$SUPORTENV_HOME/.ssh"
    chmod 700 "$SUPORTENV_HOME/.ssh"
    chown suportenv:suportenv "$SUPORTENV_HOME/.ssh"
    log "✅ Diretório SSH criado: $SUPORTENV_HOME/.ssh"
fi

# Configurar authorized_keys se necessário
if [[ ! -f "$SUPORTENV_HOME/.ssh/authorized_keys" ]]; then
    touch "$SUPORTENV_HOME/.ssh/authorized_keys"
    chmod 600 "$SUPORTENV_HOME/.ssh/authorized_keys"
    chown suportenv:suportenv "$SUPORTENV_HOME/.ssh/authorized_keys"
    info "Arquivo authorized_keys criado (vazio)"
    info "Para adicionar chaves SSH: echo 'sua_chave_publica' >> $SUPORTENV_HOME/.ssh/authorized_keys"
fi

# Configurar bashrc personalizado
log "Configurando bashrc personalizado..."
cat >> "$SUPORTENV_HOME/.bashrc" << 'EOF'

# Configurações personalizadas NVirtual
export PS1='\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ '

# Aliases úteis
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias check-visual='sudo /usr/local/bin/check_visual_boot_status.sh'
alias check-setup='sudo /usr/local/bin/check_first_boot_status.sh'

# Informações do sistema
alias sysinfo='cat /home/<USER>/SISTEMA_INFO.txt 2>/dev/null || cat /home/<USER>/SISTEMA_INFO.txt 2>/dev/null || echo "Arquivo de informações não encontrado"'

# Logs úteis
alias zabbix-proxy-log='sudo tail -f /var/log/zabbix/zabbix_proxy.log'
alias zabbix-agent-log='sudo tail -f /var/log/zabbix/zabbix_agentd.log'

echo "Bem-vindo ao Raspberry Pi NVirtual!"
echo "Usuário: suportenv | Sistema configurado automaticamente"
echo "Para verificar status: check-visual ou sysinfo"
EOF

chown suportenv:suportenv "$SUPORTENV_HOME/.bashrc"
log "✅ Bashrc personalizado configurado"

# Configurar sudoers para não pedir senha (opcional)
read -p "Deseja configurar sudo sem senha para 'suportenv'? (s/N): " SUDO_NOPASSWD
if [[ "$SUDO_NOPASSWD" =~ ^[Ss]$ ]]; then
    echo "suportenv ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/suportenv
    chmod 440 /etc/sudoers.d/suportenv
    log "✅ Sudo sem senha configurado para 'suportenv'"
    warning "⚠️ ATENÇÃO: Usuário pode executar comandos sudo sem senha!"
else
    info "Sudo com senha mantido (mais seguro)"
fi

# Criar arquivo de informações do usuário
log "Criando arquivo de informações..."
cat > "$SUPORTENV_HOME/USUARIO_INFO.txt" << EOF
=== INFORMAÇÕES DO USUÁRIO SUPORTENV ===
Data de criação: $(date)
Sistema: $(uname -a)
Desenvolvido por: Paulo Matheus - NVirtual

=== CONFIGURAÇÕES DO USUÁRIO ===
Nome: suportenv
UID: $(id -u suportenv)
GID: $(id -g suportenv)
Grupos: $(groups suportenv | cut -d: -f2)
Home: $SUPORTENV_HOME
Shell: $(getent passwd suportenv | cut -d: -f7)

=== ALIASES CONFIGURADOS ===
ll, la, l          - Listagem de arquivos
check-visual       - Verificar instalação visual
check-setup        - Verificar instalação padrão
sysinfo           - Informações do sistema
zabbix-proxy-log  - Logs do Zabbix Proxy
zabbix-agent-log  - Logs do Zabbix Agent

=== COMANDOS ÚTEIS ===
sudo systemctl status zabbix-proxy    # Status Zabbix Proxy
sudo systemctl status zabbix-agent    # Status Zabbix Agent
sudo nano /etc/zabbix/zabbix_proxy.conf  # Editar config Zabbix

=== SEGURANÇA ===
SSH configurado: $([ -d "$SUPORTENV_HOME/.ssh" ] && echo "Sim" || echo "Não")
Sudo sem senha: $([ -f "/etc/sudoers.d/suportenv" ] && echo "Sim" || echo "Não")

=== DESENVOLVIDO POR ===
Paulo Matheus - NVirtual
EOF

chown suportenv:suportenv "$SUPORTENV_HOME/USUARIO_INFO.txt"
chmod 644 "$SUPORTENV_HOME/USUARIO_INFO.txt"

log "✅ Configuração do usuário 'suportenv' concluída!"
echo
echo "=========================================="
echo "USUÁRIO SUPORTENV CONFIGURADO"
echo "=========================================="
echo
info "📋 Resumo da configuração:"
info "• Usuário: suportenv"
info "• Home: $SUPORTENV_HOME"
info "• Grupos: $(groups suportenv | cut -d: -f2)"
info "• SSH: Configurado"
info "• Bashrc: Personalizado com aliases"
echo
info "🔧 Para acessar:"
info "• Login local: su - suportenv"
info "• SSH: ssh suportenv@$(hostname -I | awk '{print $1}')"
echo
info "📄 Informações salvas em: $SUPORTENV_HOME/USUARIO_INFO.txt"
echo
warning "⚠️ PRÓXIMOS PASSOS:"
warning "1. Defina uma senha segura: sudo passwd suportenv"
warning "2. Configure chaves SSH se necessário"
warning "3. Teste o acesso: su - suportenv"
echo
log "🎯 Usuário 'suportenv' pronto para uso!"
